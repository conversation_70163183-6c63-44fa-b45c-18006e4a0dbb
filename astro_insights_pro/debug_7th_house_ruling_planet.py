"""
Debug 7th House Ruling Planet

This script debugs the 7th house ruling planet detection
and implements the missing functionality.

Version: 2.0
Author: Fortune Lens Team
Last Updated: 2025-07-31
"""

import sys
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens')

from astro_insights_pro.app.extensions import mongo
from astro_insights_pro.app import create_app
from astro_insights_pro.app.services.rule_engine.api.main_rule_engine import _fetch_chart_data_from_mongodb

def debug_7th_house_ruling_planet():
    """Debug the 7th house ruling planet detection"""
    
    print("🔍 Debugging 7th House Ruling Planet Detection")
    print("=" * 50)
    
    try:
        # Get chart data
        chart_data = _fetch_chart_data_from_mongodb(100001, 1, "D1")
        
        if chart_data and 'chart_data' in chart_data:
            d1_data = chart_data['chart_data']['D1']
            houses = d1_data.get('houses', {})
            
            print(f"✅ Found {len(houses)} houses in chart data")
            
            # Check 7th house specifically
            if '7' in houses:
                house_7 = houses['7']
                print(f"\n🏠 7th House Information:")
                print(f"   House Number: 7")
                print(f"   Sign Name: {house_7.get('sign_name', 'Unknown')}")
                print(f"   Start Degree: {house_7.get('start_degree', 'Unknown')}")
                print(f"   End Degree: {house_7.get('end_degree', 'Unknown')}")
                print(f"   Planets: {house_7.get('planets', [])}")
                print(f"   Ruling Planet: {house_7.get('ruling_planet', 'Unknown')}")
                
                # Determine ruling planet based on sign
                sign_name = house_7.get('sign_name', '').upper()
                ruling_planet = get_ruling_planet_for_sign(sign_name)
                
                print(f"\n🎯 7th House Ruling Planet Analysis:")
                print(f"   7th House Sign: {sign_name}")
                print(f"   Ruling Planet: {ruling_planet}")
                
                if ruling_planet:
                    print(f"\n📋 Next Steps:")
                    print(f"   1. Find {ruling_planet} Bhukti_Dates")
                    print(f"   2. Apply WITH_STARS_OF logic for {ruling_planet}")
                    print(f"   3. Combine with AND/OR logic")
                    print(f"   4. Apply KOCHARAM filtering")
                    
                    # Test finding the ruling planet's periods
                    test_ruling_planet_periods(ruling_planet, chart_data)
                
            else:
                print(f"❌ 7th house not found in houses data")
                print(f"   Available houses: {list(houses.keys())}")
            
            # Show all houses for reference
            print(f"\n📊 All Houses Summary:")
            for house_num, house_data in houses.items():
                sign_name = house_data.get('sign_name', 'Unknown')
                ruling_planet = get_ruling_planet_for_sign(sign_name.upper())
                print(f"   House {house_num}: {sign_name} (ruled by {ruling_planet})")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


def get_ruling_planet_for_sign(sign_name):
    """Get the ruling planet for a zodiac sign"""
    
    # Traditional Vedic astrology sign rulers
    sign_rulers = {
        'MESHA': 'MARS',        # Aries
        'RISHABA': 'VENUS',     # Taurus  
        'MITHUNA': 'MERCURY',   # Gemini
        'KATAKA': 'MOON',       # Cancer
        'SIMHA': 'SUN',         # Leo
        'KANYA': 'MERCURY',     # Virgo
        'THULA': 'VENUS',       # Libra
        'VRISCHIKA': 'MARS',    # Scorpio
        'DHANUS': 'JUPITER',    # Sagittarius
        'MAKARA': 'SATURN',     # Capricorn
        'KUMBHA': 'SATURN',     # Aquarius
        'MEENAM': 'JUPITER',    # Pisces
        
        # Alternative spellings
        'MEESHA': 'MARS',
        'RISHABHA': 'VENUS',
        'KARKATA': 'MOON',
        'KANNI': 'MERCURY',
        'TULA': 'VENUS',
        'VRISHCHIKA': 'MARS',
        'DHANUSH': 'JUPITER',
        'MAKAR': 'SATURN',
        'AQUARIUS': 'SATURN',
        'PISCES': 'JUPITER'
    }
    
    return sign_rulers.get(sign_name.upper(), 'UNKNOWN')


def test_ruling_planet_periods(ruling_planet, chart_data):
    """Test finding periods for the ruling planet"""
    
    print(f"\n🧪 Testing {ruling_planet} Period Detection")
    print("=" * 40)
    
    try:
        from astro_insights_pro.app.services.rule_engine.processors.dasha_processor import DashaProcessor
        
        processor = DashaProcessor()
        
        # Test bhukti periods for the ruling planet
        bhukti_periods = processor._get_planet_bhukti_periods(ruling_planet, chart_data)
        print(f"✅ {ruling_planet} Bhukti periods: {len(bhukti_periods)}")
        
        if bhukti_periods:
            print(f"📅 Sample {ruling_planet} Bhukti periods:")
            for i, period in enumerate(bhukti_periods[:3], 1):
                maha = period.get('maha_dasha', '?')
                bhukti = period.get('bhukti_dasha', '?')
                start_date = period.get('start_date', '?')[:10]
                print(f"   {i}. {maha}-{bhukti} - {start_date}")
            
            if len(bhukti_periods) > 3:
                print(f"   ... and {len(bhukti_periods) - 3} more periods")
        
        # Test WITH_STARS_OF logic (placeholder)
        print(f"\n⭐ WITH_STARS_OF Logic for {ruling_planet}:")
        print(f"   This would find periods where {ruling_planet}'s star")
        print(f"   is ruled by another planet, then search for that")
        print(f"   planet's dasha dates.")
        print(f"   Status: ⚠️ Needs implementation")
        
    except Exception as e:
        print(f"❌ Error testing ruling planet periods: {e}")


def test_simple_7th_house_query():
    """Test a simpler 7th house ruling planet query"""
    
    print(f"\n🧪 Testing Simplified 7th House Query")
    print("=" * 40)
    
    import requests
    
    url = "http://127.0.0.1:5003/api/rule-engine/"
    
    # Simpler query without WITH_STARS_OF
    simple_data = {
        "user_profile_id": 100001,
        "member_profile_id": 1,
        "query": "7th_House_Ruling_Planet Bhukti_Dates",
        "chart_type": "D1"
    }
    
    print(f"📋 Testing: {simple_data['query']}")
    
    try:
        response = requests.post(url, json=simple_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            periods = result.get('dasha_periods', [])
            
            print(f"✅ Simple query result: {len(periods)} periods")
            
            if periods:
                print(f"📅 Sample periods:")
                for i, period in enumerate(periods[:3], 1):
                    maha = period.get('maha_dasha', '?')
                    bhukti = period.get('bhukti_dasha', '?')
                    start_date = period.get('start_date', '?')[:10]
                    print(f"   {i}. {maha}-{bhukti} - {start_date}")
            else:
                print(f"⚠️ No periods found - 7th house ruling planet detection may need implementation")
        else:
            print(f"❌ API Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    print("🔍 7th House Ruling Planet Debug Suite")
    print("=" * 50)
    
    # Create app context
    app = create_app()
    
    with app.app_context():
        debug_7th_house_ruling_planet()
        test_simple_7th_house_query()
    
    print(f"\n🏁 7th House Debug Complete")
    print(f"   This identifies what needs to be implemented for house ruling planet queries.")
