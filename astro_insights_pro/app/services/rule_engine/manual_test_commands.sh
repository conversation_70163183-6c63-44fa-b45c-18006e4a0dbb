#!/bin/bash

# Manual Test Commands for Rule Engine Patterns
# These curl commands test the working patterns identified in comprehensive testing
# 
# Usage: Run these commands individually to test specific patterns
# Make sure the server is running on http://localhost:5003

echo "🔧 Rule Engine Manual Testing Commands"
echo "======================================"
echo ""

# Test 1: Basic Dasha Query
echo "📅 Test 1: VENUS Bhukti_Dates"
curl -X POST http://localhost:5003/api/rule-engine/ \
  -H "Content-Type: application/json" \
  -d '{
    "user_profile_id": 100001,
    "member_profile_id": 1,
    "query": "VENUS Bhukti_Dates",
    "chart_type": "D1"
  }' | jq '.success'

echo ""
echo "----------------------------------------"

# Test 2: House Ruling Planet Query
echo "🏠 Test 2: 7th House Ruling Planet Bhukti_Dates"
curl -X POST http://localhost:5003/api/rule-engine/ \
  -H "Content-Type: application/json" \
  -d '{
    "user_profile_id": 100001,
    "member_profile_id": 1,
    "query": "7th House Ruling Planet Bhukti_Dates",
    "chart_type": "D1"
  }' | jq '.success'

echo ""
echo "----------------------------------------"

# Test 3: KOCHARAM Transit with Dasha
echo "🌍 Test 3: KOCHARAM Transit with Dasha"
curl -X POST http://localhost:5003/api/rule-engine/ \
  -H "Content-Type: application/json" \
  -d '{
    "user_profile_id": 100001,
    "member_profile_id": 1,
    "query": "VENUS Bhukti_Dates AND KOCHARAM_FILTER(JUPITER IN 7th_House)",
    "chart_type": "D1"
  }' | jq '.success'

echo ""
echo "----------------------------------------"

# Test 4: KOCHARAM Aspect with Dasha
echo "👁️ Test 4: KOCHARAM Aspect with Dasha"
curl -X POST http://localhost:5003/api/rule-engine/ \
  -H "Content-Type: application/json" \
  -d '{
    "user_profile_id": 100001,
    "member_profile_id": 1,
    "query": "VENUS Bhukti_Dates AND KOCHARAM_FILTER(JUPITER ASPECT 7th_House)",
    "chart_type": "D1"
  }' | jq '.success'

echo ""
echo "----------------------------------------"

# Test 5: Logical Operators
echo "🧠 Test 5: Logical OR Query"
curl -X POST http://localhost:5003/api/rule-engine/ \
  -H "Content-Type: application/json" \
  -d '{
    "user_profile_id": 100001,
    "member_profile_id": 1,
    "query": "VENUS Bhukti_Dates OR JUPITER Bhukti_Dates",
    "chart_type": "D1"
  }' | jq '.success'

echo ""
echo "----------------------------------------"

# Test 6: Age Constraints
echo "⏰ Test 6: Age Constraint Query"
curl -X POST http://localhost:5003/api/rule-engine/ \
  -H "Content-Type: application/json" \
  -d '{
    "user_profile_id": 100001,
    "member_profile_id": 1,
    "query": "VENUS Bhukti_Dates AND Member_Age >= 25 AND Member_Age <= 35",
    "chart_type": "D1"
  }' | jq '.success'

echo ""
echo "----------------------------------------"

# Test 7: Complex Combination Query
echo "🎯 Test 7: Complex Combination Query"
curl -X POST http://localhost:5003/api/rule-engine/ \
  -H "Content-Type: application/json" \
  -d '{
    "user_profile_id": 100001,
    "member_profile_id": 1,
    "query": "(2nd House Ruling Planet Dasa_Bhukti_Dates OR 7th House Ruling Planet Bhukti_Dates OR VENUS Bhukti_Dates) AND KOCHARAM_FILTER(JUPITER ASPECT 7th_House AND JUPITER IN 7th_House)",
    "chart_type": "D1"
  }' | jq '.success'

echo ""
echo "----------------------------------------"

# Test 8: WITH_STARS_OF with Dates
echo "⭐ Test 8: WITH_STARS_OF with Dates"
curl -X POST http://localhost:5003/api/rule-engine/ \
  -H "Content-Type: application/json" \
  -d '{
    "user_profile_id": 100001,
    "member_profile_id": 1,
    "query": "7th House Ruling Planet WITH_STARS_OF VENUS Bhukti_Dates",
    "chart_type": "D1"
  }' | jq '.success'

echo ""
echo "----------------------------------------"

# Test 9: Time Duration Constraint
echo "📅 Test 9: Time Duration Constraint"
curl -X POST http://localhost:5003/api/rule-engine/ \
  -H "Content-Type: application/json" \
  -d '{
    "user_profile_id": 100001,
    "member_profile_id": 1,
    "query": "JUPITER Dasa_Dates AND PREDICTION_DURATION = 2 Years",
    "chart_type": "D1"
  }' | jq '.success'

echo ""
echo "----------------------------------------"

# Test 10: NOT Operator
echo "🚫 Test 10: NOT Operator"
curl -X POST http://localhost:5003/api/rule-engine/ \
  -H "Content-Type: application/json" \
  -d '{
    "user_profile_id": 100001,
    "member_profile_id": 1,
    "query": "NOT VENUS Bhukti_Dates",
    "chart_type": "D1"
  }' | jq '.success'

echo ""
echo "======================================"
echo "✅ All tests completed!"
echo ""
echo "Expected Results:"
echo "- All queries should return 'true' for success"
echo "- If any return 'false', check server logs"
echo "- Use 'jq .' instead of 'jq .success' to see full response"
echo ""
echo "Note: Install jq for JSON formatting: brew install jq"
