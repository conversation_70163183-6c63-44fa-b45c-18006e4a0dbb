"""
Debug House Data Structure

This script examines the actual house data structure in MongoDB
to understand how to extract sign names and ruling planets.

Version: 2.0
Author: Fortune Lens Team
Last Updated: 2025-07-31
"""

import sys
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens')

from astro_insights_pro.app.extensions import mongo
from astro_insights_pro.app import create_app
import json

def debug_raw_house_data():
    """Debug the raw house data structure in MongoDB"""
    
    print("🔍 Debugging Raw House Data Structure")
    print("=" * 45)
    
    try:
        collection = mongo.db.user_member_astro_profile_data
        
        # Get the document
        doc = collection.find_one({
            "user_profile_id": 100001,
            "member_profile_id": 1
        })
        
        if doc:
            chart_data = doc.get('chart_data', {})
            d1_data = chart_data.get('D1', {})
            
            print(f"✅ Found D1 chart data")
            print(f"   D1 keys: {list(d1_data.keys())}")
            
            # Check houses structure
            if 'houses' in d1_data:
                houses = d1_data['houses']
                print(f"\n🏠 Houses Data Structure:")
                print(f"   Type: {type(houses)}")
                print(f"   Length: {len(houses) if isinstance(houses, (list, dict)) else 'unknown'}")
                
                if isinstance(houses, list):
                    print(f"\n📋 Sample House Entries (List Format):")
                    for i, house in enumerate(houses[:3], 1):
                        print(f"   House {i}: {house}")
                        
                    # Check 7th house specifically (index 6 for 0-based)
                    if len(houses) >= 7:
                        house_7 = houses[6]  # 7th house at index 6
                        print(f"\n🎯 7th House (Index 6) Details:")
                        print(json.dumps(house_7, indent=4, default=str))
                
                elif isinstance(houses, dict):
                    print(f"\n📋 Houses Keys: {list(houses.keys())}")
                    
                    # Check 7th house
                    for key in ['7', '07', 'house_7', 'House_7']:
                        if key in houses:
                            house_7 = houses[key]
                            print(f"\n🎯 7th House (Key: {key}) Details:")
                            print(json.dumps(house_7, indent=4, default=str))
                            break
                    else:
                        # Show first few houses to understand structure
                        print(f"\n📋 Sample House Entries (Dict Format):")
                        for i, (key, house) in enumerate(houses.items()):
                            if i >= 3:
                                break
                            print(f"   {key}: {house}")
            
            # Check if there's sign information elsewhere
            print(f"\n🔍 Looking for Sign Information:")
            
            # Check chart_info
            if 'chart_info' in d1_data:
                chart_info = d1_data['chart_info']
                print(f"   chart_info keys: {list(chart_info.keys()) if isinstance(chart_info, dict) else 'Not a dict'}")
                
                # Look for sign-related fields
                sign_fields = ['signs', 'zodiac', 'rashi', 'house_signs', 'sign_names']
                for field in sign_fields:
                    if field in chart_info:
                        print(f"   Found {field}: {chart_info[field]}")
            
            # Check planets data
            if 'planets' in d1_data:
                planets = d1_data['planets']
                print(f"   planets keys: {list(planets.keys()) if isinstance(planets, dict) else 'Not a dict'}")
                
                # Check if planets have house/sign information
                if isinstance(planets, dict):
                    for planet_name, planet_data in list(planets.items())[:2]:
                        print(f"   {planet_name}: {planet_data}")
            
            # Check for lagna or ascendant information
            lagna_fields = ['lagna', 'ascendant', 'asc', 'rising_sign']
            for field in lagna_fields:
                if field in d1_data:
                    print(f"   Found {field}: {d1_data[field]}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


def check_alternative_data_sources():
    """Check if sign information is available in other collections"""
    
    print(f"\n🔍 Checking Alternative Data Sources")
    print("=" * 40)
    
    try:
        # Check if there are other collections with house/sign data
        collections = mongo.db.list_collection_names()
        
        print(f"📊 Available Collections:")
        for collection in collections:
            count = mongo.db[collection].count_documents({})
            print(f"   {collection}: {count} documents")
        
        # Check member_profile for birth chart info
        if 'member_profile' in collections:
            member_doc = mongo.db.member_profile.find_one({
                "user_profile_id": 100001,
                "member_profile_id": 1
            })
            
            if member_doc:
                print(f"\n👤 Member Profile Data:")
                print(f"   Keys: {list(member_doc.keys())}")
                
                # Look for birth chart related fields
                chart_fields = ['birth_chart', 'chart_data', 'astro_data', 'horoscope']
                for field in chart_fields:
                    if field in member_doc:
                        print(f"   Found {field}: {type(member_doc[field])}")
        
        # Check if there's a separate houses or signs collection
        house_collections = ['houses', 'signs', 'zodiac', 'astro_houses']
        for collection_name in house_collections:
            if collection_name in collections:
                sample_doc = mongo.db[collection_name].find_one()
                if sample_doc:
                    print(f"\n🏠 {collection_name} Collection Sample:")
                    print(json.dumps(sample_doc, indent=2, default=str)[:500] + "...")
        
    except Exception as e:
        print(f"❌ Error: {e}")


def suggest_implementation_approach():
    """Suggest how to implement house ruling planet detection"""
    
    print(f"\n💡 Implementation Approach Suggestions")
    print("=" * 45)
    
    print(f"🎯 Based on the data analysis, here are implementation options:")
    print()
    
    print(f"1. 🔢 Calculate from Lagna (Ascendant):")
    print(f"   • If lagna/ascendant degree is available")
    print(f"   • Calculate which sign each house falls in")
    print(f"   • Map signs to ruling planets")
    print()
    
    print(f"2. 📊 Use Planet Positions:")
    print(f"   • If planets have house/sign positions")
    print(f"   • Reverse-engineer house signs from planet positions")
    print(f"   • Apply traditional ruling planet mappings")
    print()
    
    print(f"3. 🏠 Default House-Sign Mapping:")
    print(f"   • Use traditional house-sign correspondences")
    print(f"   • House 1 = Aries (Mars), House 2 = Taurus (Venus), etc.")
    print(f"   • Adjust based on lagna if available")
    print()
    
    print(f"4. 📝 Hardcode for Testing:")
    print(f"   • For user 100001, member 1:")
    print(f"   • Assume 7th house = Libra (ruled by Venus)")
    print(f"   • Test with VENUS Bhukti_Dates")
    print(f"   • Verify KOCHARAM filtering works")
    print()
    
    print(f"🚀 Recommended Next Steps:")
    print(f"   1. Implement option 4 for immediate testing")
    print(f"   2. Test the complex query with VENUS as 7th house ruler")
    print(f"   3. Implement WITH_STARS_OF functionality")
    print(f"   4. Add proper house-sign calculation later")


if __name__ == "__main__":
    print("🔍 House Data Structure Debug Suite")
    print("=" * 50)
    
    # Create app context
    app = create_app()
    
    with app.app_context():
        debug_raw_house_data()
        check_alternative_data_sources()
        suggest_implementation_approach()
    
    print(f"\n🏁 House Data Structure Debug Complete")
    print(f"   This provides the information needed to implement house ruling planet detection.")
